"""
Configuration module for Flask Admin Panel
"""

import os

class Config:
    """Application configuration"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    FASTAPI_BASE_URL = os.environ.get('FASTAPI_BASE_URL') or 'http://localhost:8000'
    WTF_CSRF_ENABLED = True

# Static data
ROLES = [
    {'id': 1, 'name': 'Admin', 'permissions': ['view_users', 'edit_users', 'delete_users', 'manage_roles']},
    {'id': 2, 'name': 'Branch Manager', 'permissions': ['view_users', 'edit_users']},
    {'id': 3, 'name': 'Employee', 'permissions': ['view_users']},
    {'id': 4, 'name': 'Application Admin', 'permissions': ['view_users', 'edit_users']},
]

PERMISSIONS = [
    {'id': 1, 'name': 'view_users', 'description': 'Can view users'},
    {'id': 2, 'name': 'edit_users', 'description': 'Can edit users'},
    {'id': 3, 'name': 'delete_users', 'description': 'Can delete users'},
    {'id': 4, 'name': 'manage_roles', 'description': 'Can manage roles'},
    {'id': 5, 'name': 'view_applications', 'description': 'Can view applications'},
    {'id': 6, 'name': 'edit_applications', 'description': 'Can edit applications'},
    {'id': 7, 'name': 'delete_applications', 'description': 'Can delete applications'},
]
