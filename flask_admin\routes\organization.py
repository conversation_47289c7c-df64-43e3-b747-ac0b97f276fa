"""
Organization management routes for Flask Admin Panel
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from auth import login_required
from api_client import api_client, handle_api_error

organization_bp = Blueprint('organization', __name__, url_prefix='/organization')


@organization_bp.route('/branches')
@login_required
def branches():
    """Manage branches"""
    branches_data = api_client.get('/api/v1/admin/branches')
    
    if 'error' in branches_data:
        flash(f"Error loading branches: {branches_data['error']}", 'danger')
        if branches_data.get('status_code') in [401, 403]:
            return redirect(url_for('main.logout'))
        branches_data = []
    
    return render_template('organization/branches.html', branches=branches_data)


@organization_bp.route('/departments')
@login_required
def departments():
    """Manage departments"""
    departments_data = api_client.get('/api/v1/admin/departments')
    
    if 'error' in departments_data:
        flash(f"Error loading departments: {departments_data['error']}", 'danger')
        if departments_data.get('status_code') in [401, 403]:
            return redirect(url_for('main.logout'))
        departments_data = []
    
    return render_template('organization/departments.html', departments=departments_data)


@organization_bp.route('/positions')
@login_required
def positions():
    """Manage positions"""
    positions_data = api_client.get('/api/v1/admin/positions')
    
    if 'error' in positions_data:
        flash(f"Error loading positions: {positions_data['error']}", 'danger')
        if positions_data.get('status_code') in [401, 403]:
            return redirect(url_for('main.logout'))
        positions_data = []
    
    return render_template('organization/positions.html', positions=positions_data)


@organization_bp.route('/branches/create', methods=['POST'])
@login_required
def create_branch():
    """Create a new branch"""
    branch_data = {
        'name': request.form.get('name'),
        'code': request.form.get('code'),
        'address': request.form.get('address'),
        'province': request.form.get('province')
    }
    
    result = api_client.post('/api/v1/admin/branches', branch_data)
    
    if 'error' in result:
        flash(f"Error creating branch: {result['error']}", 'danger')
    else:
        flash('Branch created successfully!', 'success')
    
    return redirect(url_for('organization.branches'))


@organization_bp.route('/branches/<branch_id>/edit', methods=['POST'])
@login_required
def edit_branch(branch_id):
    """Edit an existing branch"""
    branch_data = {
        'name': request.form.get('name'),
        'code': request.form.get('code'),
        'address': request.form.get('address'),
        'province': request.form.get('province')
    }
    
    result = api_client.put(f'/api/v1/admin/branches/{branch_id}', branch_data)
    
    if 'error' in result:
        flash(f"Error updating branch: {result['error']}", 'danger')
    else:
        flash('Branch updated successfully!', 'success')
    
    return redirect(url_for('organization.branches'))


@organization_bp.route('/branches/<branch_id>/delete', methods=['POST'])
@login_required
def delete_branch(branch_id):
    """Delete a branch"""
    result = api_client.delete(f'/api/v1/admin/branches/{branch_id}')
    
    if 'error' in result:
        flash(f"Error deleting branch: {result['error']}", 'danger')
    else:
        flash('Branch deleted successfully!', 'success')
    
    return redirect(url_for('organization.branches'))


@organization_bp.route('/departments/create', methods=['POST'])
@login_required
def create_department():
    """Create a new department"""
    department_data = {
        'name': request.form.get('name'),
        'description': request.form.get('description')
    }
    
    result = api_client.post('/api/v1/admin/departments', department_data)
    
    if 'error' in result:
        flash(f"Error creating department: {result['error']}", 'danger')
    else:
        flash('Department created successfully!', 'success')
    
    return redirect(url_for('organization.departments'))


@organization_bp.route('/departments/<department_id>/edit', methods=['POST'])
@login_required
def edit_department(department_id):
    """Edit an existing department"""
    department_data = {
        'name': request.form.get('name'),
        'description': request.form.get('description')
    }
    
    result = api_client.put(f'/api/v1/admin/departments/{department_id}', department_data)
    
    if 'error' in result:
        flash(f"Error updating department: {result['error']}", 'danger')
    else:
        flash('Department updated successfully!', 'success')
    
    return redirect(url_for('organization.departments'))


@organization_bp.route('/departments/<department_id>/delete', methods=['POST'])
@login_required
def delete_department(department_id):
    """Delete a department"""
    result = api_client.delete(f'/api/v1/admin/departments/{department_id}')
    
    if 'error' in result:
        flash(f"Error deleting department: {result['error']}", 'danger')
    else:
        flash('Department deleted successfully!', 'success')
    
    return redirect(url_for('organization.departments'))


@organization_bp.route('/positions/create', methods=['POST'])
@login_required
def create_position():
    """Create a new position"""
    position_data = {
        'title': request.form.get('title'),
        'department_id': request.form.get('department_id'),
        'description': request.form.get('description')
    }
    
    result = api_client.post('/api/v1/admin/positions', position_data)
    
    if 'error' in result:
        flash(f"Error creating position: {result['error']}", 'danger')
    else:
        flash('Position created successfully!', 'success')
    
    return redirect(url_for('organization.positions'))


@organization_bp.route('/positions/<position_id>/edit', methods=['POST'])
@login_required
def edit_position(position_id):
    """Edit an existing position"""
    position_data = {
        'title': request.form.get('title'),
        'department_id': request.form.get('department_id'),
        'description': request.form.get('description')
    }
    
    result = api_client.put(f'/api/v1/admin/positions/{position_id}', position_data)
    
    if 'error' in result:
        flash(f"Error updating position: {result['error']}", 'danger')
    else:
        flash('Position updated successfully!', 'success')
    
    return redirect(url_for('organization.positions'))


@organization_bp.route('/positions/<position_id>/delete', methods=['POST'])
@login_required
def delete_position(position_id):
    """Delete a position"""
    result = api_client.delete(f'/api/v1/admin/positions/{position_id}')
    
    if 'error' in result:
        flash(f"Error deleting position: {result['error']}", 'danger')
    else:
        flash('Position deleted successfully!', 'success')
    
    return redirect(url_for('organization.positions'))
