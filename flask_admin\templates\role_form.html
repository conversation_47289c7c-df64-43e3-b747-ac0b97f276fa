{% extends "base.html" %}

{% block title %}{{ title }} - SSO Admin Panel{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">{{ title }}</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    <div class="mb-3">
                        {{ form.name.label(class="form-label") }}
                        {{ form.name(class="form-control", placeholder="Enter role name") }}
                        {% for error in form.name.errors %}
                            <div class="text-danger small">{{ error }}</div>
                        {% endfor %}
                    </div>
                    <div class="d-flex justify-content-between mt-4">
                        <a href="{{ url_for('roles.roles') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> {{ title }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
