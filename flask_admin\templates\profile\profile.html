{% extends "base.html" %}

{% block title %}My Profile - SSO Admin Panel{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="bi bi-person-circle"></i> My Profile</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-primary" onclick="editProfile()">
                <i class="bi bi-pencil"></i> Edit Profile
            </button>
        </div>
    </div>
</div>

<div class="row">
    <!-- Profile Information -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-person"></i> Profile Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center mb-4">
                        <div class="avatar-lg mx-auto mb-3">
                            <div class="avatar-title rounded-circle bg-primary text-white" style="width: 120px; height: 120px; line-height: 120px; font-size: 3rem;">
                                {{ user.username[0].upper() if user.username else 'U' }}
                            </div>
                        </div>
                        <h5>{{ user.username or 'Unknown User' }}</h5>
                        <p class="text-muted">{{ user.email or 'No email' }}</p>
                        {% if user.is_superuser %}
                            <span class="badge bg-danger">Super Admin</span>
                        {% else %}
                            <span class="badge bg-primary">Admin</span>
                        {% endif %}
                    </div>
                    <div class="col-md-8">
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong>Full Name:</strong>
                            </div>
                            <div class="col-sm-8">
                                {{ user.full_name or 'Not provided' }}
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong>Email:</strong>
                            </div>
                            <div class="col-sm-8">
                                {{ user.email or 'Not provided' }}
                                {% if user.is_verified %}
                                    <i class="bi bi-check-circle text-success" title="Verified"></i>
                                {% else %}
                                    <i class="bi bi-x-circle text-danger" title="Not verified"></i>
                                {% endif %}
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong>Status:</strong>
                            </div>
                            <div class="col-sm-8">
                                {% if user.is_active %}
                                    <span class="badge bg-success">Active</span>
                                {% else %}
                                    <span class="badge bg-danger">Inactive</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong>Last Login:</strong>
                            </div>
                            <div class="col-sm-8">
                                {{ user.last_login or 'Never' }}
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong>Member Since:</strong>
                            </div>
                            <div class="col-sm-8">
                                {{ user.created_at or 'Unknown' }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Settings -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-shield-lock"></i> Security Settings</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <h6>Password</h6>
                        <p class="text-muted">Last changed: Unknown</p>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="changePassword()">
                            <i class="bi bi-key"></i> Change Password
                        </button>
                    </div>
                    <div class="col-md-6">
                        <h6>Two-Factor Authentication</h6>
                        <p class="text-muted">Status: <span class="badge bg-warning">Not Enabled</span></p>
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="setup2FA()">
                            <i class="bi bi-shield-check"></i> Enable 2FA
                        </button>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <h6>Login Sessions</h6>
                        <p class="text-muted">Active sessions: 1</p>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="viewSessions()">
                            <i class="bi bi-list"></i> Manage Sessions
                        </button>
                    </div>
                    <div class="col-md-6">
                        <h6>API Keys</h6>
                        <p class="text-muted">Active keys: 0</p>
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="manageAPIKeys()">
                            <i class="bi bi-key"></i> Manage API Keys
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Summary -->
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-activity"></i> Activity Summary</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Total Logins</span>
                    <span class="badge bg-primary">0</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Failed Attempts</span>
                    <span class="badge bg-danger">0</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Users Created</span>
                    <span class="badge bg-success">0</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Apps Managed</span>
                    <span class="badge bg-info">0</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>Last Activity</span>
                    <span class="badge bg-secondary">Now</span>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-lightning"></i> Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-primary" onclick="window.location.href='/dashboard'">
                        <i class="bi bi-speedometer2"></i> Go to Dashboard
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="window.location.href='/users'">
                        <i class="bi bi-people"></i> Manage Users
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="window.location.href='/applications'">
                        <i class="bi bi-app"></i> Manage Apps
                    </button>
                    <button type="button" class="btn btn-outline-warning" onclick="downloadProfile()">
                        <i class="bi bi-download"></i> Export Profile
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Profile Modal -->
<div class="modal fade" id="editProfileModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Profile</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editProfileForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="editFullName" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="editFullName" value="{{ user.full_name or '' }}">
                    </div>
                    <div class="mb-3">
                        <label for="editEmail" class="form-label">Email</label>
                        <input type="email" class="form-control" id="editEmail" value="{{ user.email or '' }}">
                    </div>
                    <div class="mb-3">
                        <label for="editBio" class="form-label">Bio</label>
                        <textarea class="form-control" id="editBio" rows="3">{{ user.bio or '' }}</textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Change Password</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="changePasswordForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="currentPassword" class="form-label">Current Password</label>
                        <input type="password" class="form-control" id="currentPassword" required>
                    </div>
                    <div class="mb-3">
                        <label for="newPassword" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="newPassword" required>
                    </div>
                    <div class="mb-3">
                        <label for="confirmPassword" class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" id="confirmPassword" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Change Password</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function editProfile() {
        const modal = new bootstrap.Modal(document.getElementById('editProfileModal'));
        modal.show();
    }
    
    function changePassword() {
        const modal = new bootstrap.Modal(document.getElementById('changePasswordModal'));
        modal.show();
    }
    
    function setup2FA() {
        alert('Two-Factor Authentication setup will be implemented soon!');
    }
    
    function viewSessions() {
        alert('Session management will be implemented soon!');
    }
    
    function manageAPIKeys() {
        alert('API key management will be implemented soon!');
    }
    
    function downloadProfile() {
        alert('Profile export will be implemented soon!');
    }
    
    // Form submissions
    document.getElementById('editProfileForm').addEventListener('submit', function(e) {
        e.preventDefault();
        // TODO: Implement profile update
        alert('Profile update functionality will be implemented soon.');
    });
    
    document.getElementById('changePasswordForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        
        if (newPassword !== confirmPassword) {
            alert('Passwords do not match!');
            return;
        }
        
        // TODO: Implement password change
        alert('Password change functionality will be implemented soon.');
    });
</script>
{% endblock %}
