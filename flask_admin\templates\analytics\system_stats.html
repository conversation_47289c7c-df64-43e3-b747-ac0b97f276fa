{% extends "base.html" %}

{% block title %}System Statistics - SSO Admin Panel{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="bi bi-bar-chart"></i> System Statistics</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-secondary" onclick="location.reload()">
                <i class="bi bi-arrow-clockwise"></i> Refresh
            </button>
            <button type="button" class="btn btn-outline-primary" onclick="exportStats()">
                <i class="bi bi-download"></i> Export
            </button>
        </div>
    </div>
</div>

<!-- Overview Statistics -->
<div class="row mb-4">
    <div class="col-xl-2 col-md-4 col-sm-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Users</div>
                        <div class="h5 mb-0 font-weight-bold">{{ stats.total_users or 0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-people" style="font-size: 2rem; opacity: 0.3"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-2 col-md-4 col-sm-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Active Users</div>
                        <div class="h5 mb-0 font-weight-bold text-success">{{ stats.active_users or 0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-person-check" style="font-size: 2rem; opacity: 0.3; color: green;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-2 col-md-4 col-sm-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Verified Users</div>
                        <div class="h5 mb-0 font-weight-bold text-info">{{ stats.verified_users or 0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-shield-check" style="font-size: 2rem; opacity: 0.3; color: blue;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-2 col-md-4 col-sm-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Applications</div>
                        <div class="h5 mb-0 font-weight-bold">{{ stats.total_applications or 0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-app" style="font-size: 2rem; opacity: 0.3"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-2 col-md-4 col-sm-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Locked Users</div>
                        <div class="h5 mb-0 font-weight-bold text-warning">{{ stats.locked_users or 0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-lock" style="font-size: 2rem; opacity: 0.3; color: orange;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-2 col-md-4 col-sm-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">New (30d)</div>
                        <div class="h5 mb-0 font-weight-bold text-primary">{{ stats.recent_registrations or 0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-person-plus" style="font-size: 2rem; opacity: 0.3; color: purple;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Statistics -->
<div class="row">
    <!-- User Statistics Chart -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-graph-up"></i> User Growth Trends</h5>
            </div>
            <div class="card-body">
                <canvas id="userGrowthChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <!-- System Health -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-heart-pulse"></i> System Health</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Database</span>
                    <span class="badge bg-success">Healthy</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Cache</span>
                    <span class="badge bg-success">Healthy</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>API Response</span>
                    <span class="badge bg-success">< 100ms</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Memory Usage</span>
                    <span class="badge bg-warning">75%</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>Disk Usage</span>
                    <span class="badge bg-info">45%</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Application Statistics -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-pie-chart"></i> Application Distribution</h5>
            </div>
            <div class="card-body">
                <canvas id="appDistributionChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-activity"></i> Recent Activity Summary</h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        User Registrations (Today)
                        <span class="badge bg-primary rounded-pill">{{ stats.recent_registrations or 0 }}</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        Login Attempts (Today)
                        <span class="badge bg-success rounded-pill">0</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        Failed Logins (Today)
                        <span class="badge bg-danger rounded-pill">0</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        OAuth Authorizations (Today)
                        <span class="badge bg-info rounded-pill">0</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        API Calls (Today)
                        <span class="badge bg-warning rounded-pill">0</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Performance Metrics -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-speedometer2"></i> Performance Metrics</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Metric</th>
                                <th>Current</th>
                                <th>Average (24h)</th>
                                <th>Peak (24h)</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Response Time</td>
                                <td>85ms</td>
                                <td>92ms</td>
                                <td>156ms</td>
                                <td><span class="badge bg-success">Good</span></td>
                            </tr>
                            <tr>
                                <td>Throughput</td>
                                <td>45 req/min</td>
                                <td>38 req/min</td>
                                <td>120 req/min</td>
                                <td><span class="badge bg-success">Good</span></td>
                            </tr>
                            <tr>
                                <td>Error Rate</td>
                                <td>0.2%</td>
                                <td>0.3%</td>
                                <td>1.2%</td>
                                <td><span class="badge bg-success">Good</span></td>
                            </tr>
                            <tr>
                                <td>CPU Usage</td>
                                <td>35%</td>
                                <td>42%</td>
                                <td>78%</td>
                                <td><span class="badge bg-success">Good</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // User Growth Chart
    const userGrowthCtx = document.getElementById('userGrowthChart').getContext('2d');
    const userGrowthChart = new Chart(userGrowthCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Total Users',
                data: [12, 19, 25, 32, 45, 52],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: 'Active Users',
                data: [8, 15, 20, 28, 38, 45],
                borderColor: 'rgb(54, 162, 235)',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // Application Distribution Chart
    const appDistCtx = document.getElementById('appDistributionChart').getContext('2d');
    const appDistChart = new Chart(appDistCtx, {
        type: 'doughnut',
        data: {
            labels: ['Active Apps', 'Inactive Apps', 'Pending Apps'],
            datasets: [{
                data: [{{ stats.active_applications or 0 }}, {{ (stats.total_applications or 0) - (stats.active_applications or 0) }}, 0],
                backgroundColor: [
                    'rgba(75, 192, 192, 0.8)',
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(255, 205, 86, 0.8)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });
    
    function exportStats() {
        // TODO: Implement export functionality
        alert('Export functionality will be implemented soon!');
    }
</script>
{% endblock %}
