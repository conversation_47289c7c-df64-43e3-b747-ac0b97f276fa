{% extends "base.html" %}

{% block title %}Department Management - SSO Admin Panel{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="bi bi-diagram-3"></i> Department Management</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createDepartmentModal">
                <i class="bi bi-plus-circle"></i> New Department
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="location.reload()">
                <i class="bi bi-arrow-clockwise"></i> Refresh
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Departments</div>
                        <div class="h5 mb-0 font-weight-bold">{{ departments|length }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-diagram-3" style="font-size: 2rem; opacity: 0.3"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Active Departments</div>
                        <div class="h5 mb-0 font-weight-bold">{{ departments|length }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-check-circle" style="font-size: 2rem; opacity: 0.3; color: green;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Departments Table -->
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="mb-0">Departments List</h5>
            </div>
            <div class="col-auto">
                <div class="input-group input-group-sm">
                    <input type="text" class="form-control" placeholder="Search departments..." id="searchInput">
                    <button class="btn btn-outline-secondary" type="button">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        {% if departments %}
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="departmentsTable">
                <thead class="table-light">
                    <tr>
                        <th>Department Name</th>
                        <th>Description</th>
                        <th>Positions</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for department in departments %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm me-2">
                                    <div class="avatar-title rounded-circle bg-info text-white">
                                        {{ department.name[0].upper() if department.name else 'D' }}
                                    </div>
                                </div>
                                <div>
                                    <div class="fw-semibold">{{ department.name }}</div>
                                </div>
                            </div>
                        </td>
                        <td>{{ department.description or '-' }}</td>
                        <td>
                            <span class="badge bg-secondary">0 positions</span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-primary" 
                                        onclick="editDepartment('{{ department.id }}', '{{ department.name }}', '{{ department.description }}')"
                                        title="Edit">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button type="button" class="btn btn-outline-info" 
                                        onclick="viewPositions('{{ department.id }}', '{{ department.name }}')"
                                        title="View Positions">
                                    <i class="bi bi-briefcase"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger" 
                                        onclick="deleteDepartment('{{ department.id }}', '{{ department.name }}')"
                                        title="Delete">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-diagram-3 text-muted" style="font-size: 4rem;"></i>
            <h5 class="mt-3 text-muted">No Departments Found</h5>
            <p class="text-muted">Create your first department to get started.</p>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createDepartmentModal">
                <i class="bi bi-plus-circle"></i> Create First Department
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- Create Department Modal -->
<div class="modal fade" id="createDepartmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Department</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="createDepartmentForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="departmentName" class="form-label">Department Name *</label>
                        <input type="text" class="form-control" id="departmentName" required>
                    </div>
                    <div class="mb-3">
                        <label for="departmentDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="departmentDescription" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Department</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Department Modal -->
<div class="modal fade" id="editDepartmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Department</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editDepartmentForm">
                <input type="hidden" id="editDepartmentId">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="editDepartmentName" class="form-label">Department Name *</label>
                        <input type="text" class="form-control" id="editDepartmentName" required>
                    </div>
                    <div class="mb-3">
                        <label for="editDepartmentDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="editDepartmentDescription" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Department</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function editDepartment(id, name, description) {
        document.getElementById('editDepartmentId').value = id;
        document.getElementById('editDepartmentName').value = name;
        document.getElementById('editDepartmentDescription').value = description || '';
        
        const modal = new bootstrap.Modal(document.getElementById('editDepartmentModal'));
        modal.show();
    }
    
    function viewPositions(id, name) {
        // Navigate to positions page filtered by department
        window.location.href = `/organization/positions?department=${id}`;
    }
    
    function deleteDepartment(id, name) {
        if (confirm(`Are you sure you want to delete the department "${name}"? This action cannot be undone.`)) {
            // TODO: Implement delete functionality
            alert('Delete functionality will be implemented soon.');
        }
    }
    
    // Form submissions
    document.getElementById('createDepartmentForm').addEventListener('submit', function(e) {
        e.preventDefault();
        // TODO: Implement create functionality
        alert('Create functionality will be implemented soon.');
    });
    
    document.getElementById('editDepartmentForm').addEventListener('submit', function(e) {
        e.preventDefault();
        // TODO: Implement update functionality
        alert('Update functionality will be implemented soon.');
    });
    
    // Search functionality
    document.getElementById('searchInput').addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const table = document.getElementById('departmentsTable');
        const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
        
        for (let row of rows) {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        }
    });
</script>
{% endblock %}
