#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create or update the admin user as a superuser
"""

import sys
import os

# Add project root to Python path
sys.path.append(os.path.abspath(os.path.dirname(__file__)))

from sqlalchemy.orm import Session
from app.core.database import Session<PERSON><PERSON><PERSON>
from app.models.user import User
from app.core.security import hash_password

def create_or_update_admin():
    """Create or update the admin user as a superuser"""
    db: Session = SessionLocal()

    try:
        # Find the admin user by username or email
        admin_user = db.query(User).filter(
            (User.username == "admin") | (User.email == "<EMAIL>")
        ).first()

        if admin_user:
            print("📝 Admin user found, updating to superuser...")
            # Update existing admin user to be superuser
            admin_user.is_superuser = True
            admin_user.is_active = True
            admin_user.is_verified = True
            admin_user.username = "admin"  # Ensure username is admin
        else:
            print("🆕 Creating new admin user...")
            # Create new admin user
            hashed_password = hash_password("password")
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                hashed_password=hashed_password,
                full_name="System Administrator",
                is_active=True,
                is_verified=True,
                is_superuser=True
            )
            db.add(admin_user)

        db.commit()

        print("✅ Admin user created/updated successfully!")
        print(f"   Username: {admin_user.username}")
        print(f"   Email: {admin_user.email}")
        print(f"   Is Superuser: {admin_user.is_superuser}")
        print(f"   Is Active: {admin_user.is_active}")
        print(f"   Is Verified: {admin_user.is_verified}")
        print("\n🔑 You can now login with:")
        print("   Username: admin")
        print("   Password: password")

        return True

    except Exception as e:
        print(f"❌ Error creating/updating admin user: {e}")
        db.rollback()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    print("🔧 Creating/updating admin user as superuser...")
    success = create_or_update_admin()

    if not success:
        sys.exit(1)
