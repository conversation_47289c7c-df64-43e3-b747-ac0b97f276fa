{% extends "base.html" %}

{% block title %}Branch Management - SSO Admin Panel{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="bi bi-geo-alt"></i> Branch Management</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createBranchModal">
                <i class="bi bi-plus-circle"></i> New Branch
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="location.reload()">
                <i class="bi bi-arrow-clockwise"></i> Refresh
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Branches</div>
                        <div class="h5 mb-0 font-weight-bold">{{ branches|length }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-geo-alt" style="font-size: 2rem; opacity: 0.3"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Active Branches</div>
                        <div class="h5 mb-0 font-weight-bold">{{ branches|length }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-check-circle" style="font-size: 2rem; opacity: 0.3; color: green;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Branches Table -->
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="mb-0">Branches List</h5>
            </div>
            <div class="col-auto">
                <div class="input-group input-group-sm">
                    <input type="text" class="form-control" placeholder="Search branches..." id="searchInput">
                    <button class="btn btn-outline-secondary" type="button">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        {% if branches %}
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="branchesTable">
                <thead class="table-light">
                    <tr>
                        <th>Branch Name</th>
                        <th>Branch Code</th>
                        <th>Address</th>
                        <th>Province</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for branch in branches %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm me-2">
                                    <div class="avatar-title rounded-circle bg-primary text-white">
                                        {{ branch.name[0].upper() if branch.name else 'B' }}
                                    </div>
                                </div>
                                <div>
                                    <div class="fw-semibold">{{ branch.name }}</div>
                                </div>
                            </div>
                        </td>
                        <td><span class="badge bg-secondary">{{ branch.code }}</span></td>
                        <td>{{ branch.address or '-' }}</td>
                        <td>{{ branch.province or '-' }}</td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-primary" 
                                        onclick="editBranch('{{ branch.id }}', '{{ branch.name }}', '{{ branch.code }}', '{{ branch.address }}', '{{ branch.province }}')"
                                        title="Edit">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger" 
                                        onclick="deleteBranch('{{ branch.id }}', '{{ branch.name }}')"
                                        title="Delete">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-geo-alt text-muted" style="font-size: 4rem;"></i>
            <h5 class="mt-3 text-muted">No Branches Found</h5>
            <p class="text-muted">Create your first branch to get started.</p>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createBranchModal">
                <i class="bi bi-plus-circle"></i> Create First Branch
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- Create Branch Modal -->
<div class="modal fade" id="createBranchModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Branch</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="createBranchForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="branchName" class="form-label">Branch Name *</label>
                        <input type="text" class="form-control" id="branchName" required>
                    </div>
                    <div class="mb-3">
                        <label for="branchCode" class="form-label">Branch Code *</label>
                        <input type="text" class="form-control" id="branchCode" required>
                    </div>
                    <div class="mb-3">
                        <label for="branchAddress" class="form-label">Address</label>
                        <textarea class="form-control" id="branchAddress" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="branchProvince" class="form-label">Province</label>
                        <input type="text" class="form-control" id="branchProvince">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Branch</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Branch Modal -->
<div class="modal fade" id="editBranchModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Branch</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editBranchForm">
                <input type="hidden" id="editBranchId">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="editBranchName" class="form-label">Branch Name *</label>
                        <input type="text" class="form-control" id="editBranchName" required>
                    </div>
                    <div class="mb-3">
                        <label for="editBranchCode" class="form-label">Branch Code *</label>
                        <input type="text" class="form-control" id="editBranchCode" required>
                    </div>
                    <div class="mb-3">
                        <label for="editBranchAddress" class="form-label">Address</label>
                        <textarea class="form-control" id="editBranchAddress" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="editBranchProvince" class="form-label">Province</label>
                        <input type="text" class="form-control" id="editBranchProvince">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Branch</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function editBranch(id, name, code, address, province) {
        document.getElementById('editBranchId').value = id;
        document.getElementById('editBranchName').value = name;
        document.getElementById('editBranchCode').value = code;
        document.getElementById('editBranchAddress').value = address || '';
        document.getElementById('editBranchProvince').value = province || '';
        
        const modal = new bootstrap.Modal(document.getElementById('editBranchModal'));
        modal.show();
    }
    
    function deleteBranch(id, name) {
        if (confirm(`Are you sure you want to delete the branch "${name}"? This action cannot be undone.`)) {
            // TODO: Implement delete functionality
            alert('Delete functionality will be implemented soon.');
        }
    }
    
    // Form submissions
    document.getElementById('createBranchForm').addEventListener('submit', function(e) {
        e.preventDefault();
        // TODO: Implement create functionality
        alert('Create functionality will be implemented soon.');
    });
    
    document.getElementById('editBranchForm').addEventListener('submit', function(e) {
        e.preventDefault();
        // TODO: Implement update functionality
        alert('Update functionality will be implemented soon.');
    });
    
    // Search functionality
    document.getElementById('searchInput').addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const table = document.getElementById('branchesTable');
        const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
        
        for (let row of rows) {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        }
    });
</script>
{% endblock %}
